import { FakeListChatModel } from '@langchain/core/utils/testing';
import { beforeEach, describe, expect, it } from 'vitest';

import type { Job } from '../entities/job';
import { TransferableSkillAnalyzer } from './transferable-skill-analyzer';
import type { ConsolidatedSkill } from './types';

describe('TransferableSkillAnalyzer', () => {
  let analyzer: TransferableSkillAnalyzer;
  let fakeModel: FakeListChatModel;

  beforeEach(() => {
    fakeModel = new FakeListChatModel({
      responses: [],
    });

    analyzer = new TransferableSkillAnalyzer(fakeModel);
  });

  describe('analyzeTransferableSkills', () => {
    it('should return empty array when no unmatched resume skills', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [];
      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX', 'Hooks'] },
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toEqual([]);
    });

    it('should return empty array when no unmatched job skills', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
      ];
      const unmatchedJobSkills: Job['skills'] = [];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toEqual([]);
    });

    it('should analyze transferable skills and return matches', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL'],
          sourceResumes: [1],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX', 'Hooks'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
      ];

      // Configure fake model with AI response
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning:
                'Both are modern frontend frameworks with similar component-based architecture',
            },
            {
              resumeSkill: 'MySQL',
              jobSkill: 'PostgreSQL',
              confidenceRating: 3,
              reasoning:
                'Both are relational databases with similar SQL syntax',
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        jobSkill: 'PostgreSQL',
        resumeSkill: 'MySQL',
        confidenceRating: 3,
        reasoning: 'Both are relational databases with similar SQL syntax.',
        sourceResume: undefined,
      });
      expect(result[1]).toEqual({
        jobSkill: 'React',
        resumeSkill: 'Vue',
        confidenceRating: 2,
        reasoning:
          'Both are modern frontend frameworks with similar component-based architecture.',
        sourceResume: undefined,
      });
    });

    it('should handle AI processing errors gracefully', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
      ];

      // Configure fake model with invalid JSON to trigger error
      fakeModel.responses = ['invalid json response'];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toEqual([]);
    });

    it('should filter out matches with confidence threshold', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
      ];

      // Configure fake model with AI response containing different confidence ratings
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 1, // Low confidence
              reasoning:
                'Both are frontend frameworks with different architectures',
            },
            {
              resumeSkill: 'MySQL',
              jobSkill: 'PostgreSQL',
              confidenceRating: 3, // High confidence
              reasoning:
                'Both are relational databases with similar SQL syntax',
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills,
        { confidenceThreshold: 2 } // Only include matches with confidence >= 2
      );

      // Should only include the high confidence match
      expect(result).toHaveLength(1);
      expect(result[0].resumeSkill).toBe('MySQL');
      expect(result[0].confidenceRating).toBe(3);
    });

    it('should filter out matches with invalid reasoning', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
      ];

      // Configure fake model with AI response containing invalid reasoning
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning: 'similar', // Too generic/short
            },
            {
              resumeSkill: 'MySQL',
              jobSkill: 'PostgreSQL',
              confidenceRating: 3,
              reasoning:
                'Both are relational database management systems with similar SQL syntax and functionality',
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      // Should only include the match with valid reasoning
      expect(result).toHaveLength(1);
      expect(result[0].resumeSkill).toBe('MySQL');
      expect(result[0].reasoning).toBe(
        'Both are relational database management systems with similar SQL syntax and functionality.'
      );
    });

    it('should clean and format reasoning text', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
      ];

      // Configure fake model with AI response containing reasoning that needs cleaning
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning:
                '  both are modern frontend frameworks with component-based architecture  ', // Needs trimming and capitalization
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toHaveLength(1);
      expect(result[0].reasoning).toBe(
        'Both are modern frontend frameworks with component-based architecture.'
      );
    });

    it('should deduplicate similar skill comparisons', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'Vue.js',
          level: 'Advanced',
          keywords: ['Vue'],
          sourceResumes: [1],
        }, // Similar to above
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
      ];

      // Configure fake model with AI response
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning: 'Both are frontend frameworks',
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      // Should process and return results (deduplication happens internally)
      expect(result).toHaveLength(1);
      expect(result[0].resumeSkill).toBe('Vue');
      expect(result[0].jobSkill).toBe('React');
    });

    it('should optimize skill comparisons by priority', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js', 'Components'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL', 'Database'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX', 'Components'] }, // Has keyword overlap with Vue
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] }, // Has keyword overlap with MySQL
      ];

      // Configure fake model with AI response
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning:
                'Both are frontend frameworks with component-based architecture',
            },
            {
              resumeSkill: 'MySQL',
              jobSkill: 'PostgreSQL',
              confidenceRating: 3,
              reasoning: 'Both are relational databases with SQL support',
            },
          ],
        }),
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toHaveLength(2);
      // Results should be sorted by confidence rating (highest first)
      expect(result[0].confidenceRating).toBe(3);
      expect(result[1].confidenceRating).toBe(2);
    });

    it('should include sourceResume when includeSourceResume option is set', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [1],
        },
      ];
      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
      ];
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning: 'Both are frontend frameworks',
            },
          ],
        }),
      ];
      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills,
        { includeSourceResume: true }
      );
      expect(result).toHaveLength(1);
      expect(result[0].sourceResume).toBe(1);
    });

    it('should limit the number of results if maxTransferableSkills is set', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL'],
          sourceResumes: [0],
        },
      ];
      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
      ];
      fakeModel.responses = [
        JSON.stringify({
          matches: [
            {
              resumeSkill: 'Vue',
              jobSkill: 'React',
              confidenceRating: 2,
              reasoning: 'Both are frontend frameworks',
            },
            {
              resumeSkill: 'MySQL',
              jobSkill: 'PostgreSQL',
              confidenceRating: 3,
              reasoning: 'Both are relational databases',
            },
          ],
        }),
      ];
      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills,
        { maxTransferableSkills: 1 }
      );
      expect(result).toHaveLength(1);
    });
  });

  describe('identifyMissingSkills', () => {
    it('should identify skills that have no direct or transferable matches', () => {
      const jobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'Node.js', level: 'Senior', keywords: ['Express'] },
        { name: 'Docker', level: null, keywords: ['Containers'] },
        { name: 'Kubernetes', level: null, keywords: ['K8s'] },
      ];

      const directMatches = ['React']; // React has a direct match
      const transferableMatches = ['Node.js']; // Node.js has a transferable match

      const result = analyzer.identifyMissingSkills(
        jobSkills,
        directMatches,
        transferableMatches
      );

      expect(result).toHaveLength(2);
      expect(result).toEqual([
        {
          name: 'Docker',
          level: null,
          keywords: ['Containers'],
          category: 'general',
        },
        {
          name: 'Kubernetes',
          level: null,
          keywords: ['K8s'],
          category: 'general',
        },
      ]);
    });

    it('should return empty array when no job skills provided', () => {
      const result = analyzer.identifyMissingSkills(null, ['React'], ['Vue']);

      expect(result).toEqual([]);
    });

    it('should return empty array when all job skills are matched', () => {
      const jobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'Vue', level: null, keywords: ['Components'] },
      ];

      const directMatches = ['React'];
      const transferableMatches = ['Vue'];

      const result = analyzer.identifyMissingSkills(
        jobSkills,
        directMatches,
        transferableMatches
      );

      expect(result).toEqual([]);
    });

    it('should categorize missing skills correctly', () => {
      const jobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
        { name: 'AWS', level: null, keywords: ['EC2', 'S3'] },
        { name: 'Jest', level: null, keywords: ['Testing'] },
      ];

      const directMatches: string[] = [];
      const transferableMatches: string[] = [];

      const result = analyzer.identifyMissingSkills(
        jobSkills,
        directMatches,
        transferableMatches
      );

      expect(result).toHaveLength(4);
      expect(result.find((skill) => skill.name === 'React')?.category).toBe(
        'frontend-framework'
      );
      expect(
        result.find((skill) => skill.name === 'PostgreSQL')?.category
      ).toBe('database');
      expect(result.find((skill) => skill.name === 'AWS')?.category).toBe(
        'cloud-platform'
      );
      expect(result.find((skill) => skill.name === 'Jest')?.category).toBe(
        'testing-tool'
      );
    });

    it('should handle case-insensitive matching', () => {
      const jobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'Vue', level: null, keywords: ['Components'] },
      ];

      const directMatches = ['react']; // lowercase
      const transferableMatches = ['VUE']; // uppercase

      const result = analyzer.identifyMissingSkills(
        jobSkills,
        directMatches,
        transferableMatches
      );

      expect(result).toEqual([]);
    });
  });
});
